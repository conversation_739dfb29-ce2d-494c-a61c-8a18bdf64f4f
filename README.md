# FastAPI 示例项目

## 安装依赖

```bash
pip install -r requirements.txt
```

## 启动服务

```bash
uvicorn main:app --reload
```

访问: http://127.0.0.1:8000/

## API 接口

### 1. 根路径
- **GET** `/`
- 返回欢迎信息

### 2. 文件上传接口
- **POST** `/upload`
- 参数：
  - `api_key`: API密钥（必需）
  - `file`: 上传的文件（必需）
  - `file_type`: 文件类型（可选，默认为"image"）

#### 使用示例（curl）：
```bash
curl -X POST "http://127.0.0.1:8000/upload" \
  -H "Content-Type: multipart/form-data" \
  -F "api_key=your_api_key_here" \
  -F "file=@/path/to/your/file.png" \
  -F "file_type=image"
```

#### 使用示例（Python requests）：
```python
import requests

url = "http://127.0.0.1:8000/upload"
files = {
    'file': open('your_file.png', 'rb')
}
data = {
    'api_key': 'your_api_key_here',
    'file_type': 'image'
}

response = requests.post(url, files=files, data=data)
print(response.json())
```

## API 文档

启动服务后，可以访问以下地址查看自动生成的API文档：
- Swagger UI: http://127.0.0.1:8000/docs
- ReDoc: http://127.0.0.1:8000/redoc 