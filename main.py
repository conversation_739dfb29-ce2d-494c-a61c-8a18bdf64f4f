from fastapi import Fast<PERSON><PERSON>, File, UploadFile, Form, Request
from fastapi.responses import JSONResponse, HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
import http.client
import mimetypes
from codecs import encode
import os
import tempfile
import json  # 新增

app = FastAPI()

# 创建静态文件目录
os.makedirs("static", exist_ok=True)
app.mount("/static", StaticFiles(directory="static"), name="static")

# 创建templates目录
os.makedirs("templates", exist_ok=True)
templates = Jinja2Templates(directory="templates")

@app.get("/", response_class=HTMLResponse)
async def read_root(request: Request):
    return templates.TemplateResponse("index.html", {"request": request})

@app.get("/api/health")
def health_check():
    return {"status": "healthy"}

@app.post("/api/upload")
async def upload_file(
    file: UploadFile = File(...),
    file_type: str = Form(default="image")
):
    """
    文件上传接口
    """
    try:
        # 固定的API Key
        api_key = "979550fb2b0047509bc9187fbb75d0e2"
        
        # 创建临时文件保存上传的文件
        with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(file.filename)[1]) as temp_file:
            content = await file.read()
            temp_file.write(content)
            temp_file_path = temp_file.name
        
        # 构建multipart/form-data请求
        dataList = []
        boundary = 'wL36Yn8afVp8Ag7AmP8qZ0SA4n1v9T'
        
        # 添加apiKey字段
        dataList.append(encode('--' + boundary))
        dataList.append(encode('Content-Disposition: form-data; name=apiKey;'))
        dataList.append(encode('Content-Type: {}'.format('text/plain')))
        dataList.append(encode(''))
        dataList.append(encode(api_key))
        
        # 添加文件字段
        dataList.append(encode('--' + boundary))
        dataList.append(encode('Content-Disposition: form-data; name=file; filename={0}'.format(file.filename)))
        
        fileType = mimetypes.guess_type(file.filename)[0] or 'application/octet-stream'
        dataList.append(encode('Content-Type: {}'.format(fileType)))
        dataList.append(encode(''))
        
        with open(temp_file_path, 'rb') as f:
            dataList.append(f.read())
        
        # 添加fileType字段
        dataList.append(encode('--' + boundary))
        dataList.append(encode('Content-Disposition: form-data; name=fileType;'))
        dataList.append(encode('Content-Type: {}'.format('text/plain')))
        dataList.append(encode(''))
        dataList.append(encode(file_type))
        
        dataList.append(encode('--'+boundary+'--'))
        dataList.append(encode(''))
        
        body = b'\r\n'.join(dataList)
        
        # 发送HTTP请求
        conn = http.client.HTTPSConnection("www.runninghub.cn")
        headers = {
            'Host': 'www.runninghub.cn',
            'Content-type': 'multipart/form-data; boundary={}'.format(boundary)
        }
        
        conn.request("POST", "/task/openapi/upload", body, headers)
        res = conn.getresponse()
        data = res.read()
        
        # 清理临时文件
        os.unlink(temp_file_path)
        
        # 解析返回内容，提取fileName
        try:
            resp_json = json.loads(data.decode("utf-8"))
            file_name = resp_json.get("data", {}).get("fileName")
        except Exception as e:
            file_name = None
        
        return JSONResponse(
            content={"message": "上传成功", "fileName": file_name, "raw_response": data.decode("utf-8")},
            status_code=200
        )
    except Exception as e:
        # 确保清理临时文件
        if 'temp_file_path' in locals():
            try:
                os.unlink(temp_file_path)
            except:
                pass
        
        return JSONResponse(
            content={"error": f"上传失败: {str(e)}"},
            status_code=500
        )

@app.post("/api/create_task")
async def create_task(
    # workflow_id: str = Form(...),
    original_file_name: str = Form(...),
    target_file_name: str = Form(...)
):
    """
    创建任务接口
    """
    try:
        # 固定的API Key
        api_key = "979550fb2b0047509bc9187fbb75d0e2"
        
        # 组装nodeInfoList
        node_info_list = [
            {"nodeId": "241", "fieldName": "image", "fieldValue": original_file_name},
            {"nodeId": "242", "fieldName": "image", "fieldValue": target_file_name}
        ]
        
        # 调用创建任务接口
        conn = http.client.HTTPSConnection("www.runninghub.cn")
        headers = {
            'Host': 'www.runninghub.cn',
            'Content-Type': 'application/json'
        }
        body = json.dumps({
            # "workflowId": workflow_id,
            "webappId": "1945352105341198337",
            "apiKey": api_key,
            "nodeInfoList": node_info_list
        })
        
        conn.request("POST", "/task/openapi/ai-app/run", body, headers)
        res = conn.getresponse()
        data = res.read()
        response_data = json.loads(data.decode("utf-8"))
        
        # 提取taskId
        task_id = response_data.get("data", {}).get("taskId")
        
        return JSONResponse(
            content={
                "message": "创建任务成功", 
                "taskId": task_id,
                "response": data.decode("utf-8")
            },
            status_code=200
        )
    except Exception as e:
        return JSONResponse(
            content={"error": f"创建任务失败: {str(e)}"},
            status_code=500
        )

@app.get("/api/check_task/{task_id}")
async def check_task(task_id: str):
    """
    检查任务执行状态
    """
    try:
        # 固定的API Key
        api_key = "979550fb2b0047509bc9187fbb75d0e2"
        
        conn = http.client.HTTPSConnection("www.runninghub.cn")
        payload = json.dumps({
            "apiKey": api_key,
            "taskId": task_id
        })
        headers = {
            'Host': 'www.runninghub.cn',
            'Content-Type': 'application/json'
        }
        
        conn.request("POST", "/task/openapi/outputs", payload, headers)
        res = conn.getresponse()
        data = res.read()
        response_data = json.loads(data.decode("utf-8"))
        
        return JSONResponse(
            content={
                "message": "查询成功",
                "response": response_data
            },
            status_code=200
        )
    except Exception as e:
        return JSONResponse(
            content={"error": f"查询任务失败: {str(e)}"},
            status_code=500
        ) 