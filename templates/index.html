<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI图片生成任务</title>
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#6366f1',
                        secondary: '#8b5cf6',
                        accent: '#ec4899',
                        neutral: '#1f2937',
                    },
                    fontFamily: {
                        inter: ['Inter', 'system-ui', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            .content-auto {
                content-visibility: auto;
            }
            .gradient-bg {
                background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #ec4899 100%);
            }
            .gradient-text {
                background-clip: text;
                -webkit-background-clip: text;
                color: transparent;
                background-image: linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #ec4899 100%);
            }
            .card-shadow {
                box-shadow: 0 10px 25px -5px rgba(99, 102, 241, 0.1), 0 8px 10px -6px rgba(99, 102, 241, 0.1);
            }
            .image-preview {
                transition: all 0.3s ease;
            }
            .image-preview:hover {
                transform: translateY(-5px);
                box-shadow: 0 15px 30px -10px rgba(99, 102, 241, 0.2);
            }
            .btn-hover {
                transition: all 0.3s ease;
            }
            .btn-hover:hover {
                transform: translateY(-2px);
                box-shadow: 0 10px 20px -10px rgba(99, 102, 241, 0.4);
            }
            .file-input-label {
                transition: all 0.3s ease;
            }
            .file-input-label:hover {
                background-color: rgba(99, 102, 241, 0.1);
            }
        }
    </style>
</head>
<body class="bg-gray-50 font-inter min-h-screen">
    <div class="container mx-auto px-4 py-12 max-w-4xl">
        <!-- 页面标题 -->
        <div class="text-center mb-10">
            <h1 class="text-[clamp(1.8rem,4vw,2.5rem)] font-bold gradient-text mb-3">AI发型生成任务</h1>
        </div>
        
        <!-- 主卡片 -->
        <div class="bg-white rounded-2xl card-shadow p-6 md:p-8 mb-8">
            <form id="ai-form" class="space-y-6">
               
                
                <!-- 图片上传区域 -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- 原图上传 -->
                    <div class="form-group">
                        <label for="originalImage" class="block text-gray-700 font-medium mb-2 flex items-center">
                            <i class="fa fa-image text-primary mr-2"></i>原图上传
                        </label>
                        
                        <!-- 上传区域 -->
                        <label for="originalImage" class="file-input-label block border-2 border-dashed border-gray-300 rounded-lg p-4 text-center cursor-pointer">
                            <input type="file" id="originalImage" name="originalImage" accept="image/*" required 
                                   class="hidden">
                            <div id="originalPlaceholder" class="py-6">
                                <i class="fa fa-cloud-upload text-4xl text-gray-400 mb-2"></i>
                                <p class="text-gray-500">点击或拖拽图片到此处上传</p>
                                <p class="text-gray-400 text-xs mt-1">支持JPG、PNG等格式</p>
                            </div>
                            
                            <!-- 预览区域 -->
                            <div id="originalPreview" class="image-preview hidden">
                                <img id="originalImagePreview" src="" alt="原图预览" 
                                     class="w-full h-auto rounded-lg object-cover">
                                <button type="button" id="clearOriginal" class="mt-2 text-sm text-red-500 hover:text-red-700 transition-colors">
                                    <i class="fa fa-times-circle mr-1"></i>移除图片
                                </button>
                            </div>
                        </label>
                        <p class="text-gray-500 text-sm mt-1">原始图像</p>
                    </div>
                    
                    <!-- 目标图上传 -->
                    <div class="form-group">
                        <label for="targetImage" class="block text-gray-700 font-medium mb-2 flex items-center">
                            <i class="fa fa-picture-o text-primary mr-2"></i>目标图上传
                        </label>
                        
                        <!-- 上传区域 -->
                        <label for="targetImage" class="file-input-label block border-2 border-dashed border-gray-300 rounded-lg p-4 text-center cursor-pointer">
                            <input type="file" id="targetImage" name="targetImage" accept="image/*" required 
                                   class="hidden">
                            <div id="targetPlaceholder" class="py-6">
                                <i class="fa fa-cloud-upload text-4xl text-gray-400 mb-2"></i>
                                <p class="text-gray-500">点击或拖拽图片到此处上传</p>
                                <p class="text-gray-400 text-xs mt-1">支持JPG、PNG等格式</p>
                            </div>
                            
                            <!-- 预览区域 -->
                            <div id="targetPreview" class="image-preview hidden">
                                <img id="targetImagePreview" src="" alt="目标图预览" 
                                     class="w-full h-auto rounded-lg object-cover">
                                <button type="button" id="clearTarget" class="mt-2 text-sm text-red-500 hover:text-red-700 transition-colors">
                                    <i class="fa fa-times-circle mr-1"></i>移除图片
                                </button>
                            </div>
                        </label>
                        <p class="text-gray-500 text-sm mt-1">发型参考图像</p>
                    </div>
                </div>
                
                <!-- 提交按钮 -->
                <button type="submit" class="btn-hover w-full gradient-bg text-white font-bold py-3 px-6 rounded-lg text-lg flex items-center justify-center">
                    <i class="fa fa-magic mr-2"></i>开始生成
                </button>
            </form>
            
            <!-- 进度条 -->
            <div class="progress-container mt-6 hidden" id="progressContainer">
                <div class="flex justify-between items-center mb-1">
                    <span class="text-sm font-medium text-gray-700">处理进度</span>
                    <span class="text-sm font-medium text-primary" id="progressPercent">0%</span>
                </div>
                <div class="progress-bar h-2 bg-gray-200 rounded-full overflow-hidden">
                    <div class="progress-fill gradient-bg h-full w-0 transition-all duration-500 ease-out" id="progressFill"></div>
                </div>
                <div class="status-text text-center text-gray-600 mt-3 flex items-center justify-center">
                    <i class="fa fa-circle-o-notch fa-spin mr-2"></i>
                    <span id="statusText">正在处理中...</span>
                </div>
            </div>
            
            <!-- 结果显示 -->
            <div class="result mt-6 p-4 bg-gray-50 rounded-lg hidden" id="result">
                <h3 class="text-lg font-semibold text-gray-800 mb-3 flex items-center">
                    <i class="fa fa-check-circle text-green-500 mr-2"></i>生成结果
                </h3>
                <div class="result-image mt-4">
                    <img id="resultImage" src="" alt="AI生成的图片" class="w-full h-auto rounded-lg shadow-md">
                </div>
                <div class="mt-4 flex justify-center">
                    <a href="#" id="downloadLink" class="inline-flex items-center text-primary hover:text-secondary transition-colors">
                        <i class="fa fa-download mr-1"></i>下载生成结果
                    </a>
                </div>
            </div>
        </div>
        
        
    </div>
    
    <script>
        const form = document.getElementById('ai-form');
        const resultDiv = document.getElementById('result');
        const progressContainer = document.getElementById('progressContainer');
        const progressFill = document.getElementById('progressFill');
        const progressPercent = document.getElementById('progressPercent');
        const statusText = document.getElementById('statusText');
        const resultImage = document.getElementById('resultImage');
        const downloadLink = document.getElementById('downloadLink');
        
        // 图片上传和预览相关元素
        const originalImage = document.getElementById('originalImage');
        const targetImage = document.getElementById('targetImage');
        const originalPlaceholder = document.getElementById('originalPlaceholder');
        const targetPlaceholder = document.getElementById('targetPlaceholder');
        const originalPreview = document.getElementById('originalPreview');
        const targetPreview = document.getElementById('targetPreview');
        const originalImagePreview = document.getElementById('originalImagePreview');
        const targetImagePreview = document.getElementById('targetImagePreview');
        const clearOriginal = document.getElementById('clearOriginal');
        const clearTarget = document.getElementById('clearTarget');
        
        let checkInterval = null;
        
        // 原图预览
        originalImage.addEventListener('change', function(e) {
            if (e.target.files && e.target.files[0]) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    originalImagePreview.src = e.target.result;
                    originalPlaceholder.classList.add('hidden');
                    originalPreview.classList.remove('hidden');
                }
                reader.readAsDataURL(e.target.files[0]);
            }
        });
        
        // 目标图预览
        targetImage.addEventListener('change', function(e) {
            if (e.target.files && e.target.files[0]) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    targetImagePreview.src = e.target.result;
                    targetPlaceholder.classList.add('hidden');
                    targetPreview.classList.remove('hidden');
                }
                reader.readAsDataURL(e.target.files[0]);
            }
        });
        
        // 清除原图
        clearOriginal.addEventListener('click', function(e) {
            e.preventDefault();
            originalImage.value = '';
            originalPreview.classList.add('hidden');
            originalPlaceholder.classList.remove('hidden');
        });
        
        // 清除目标图
        clearTarget.addEventListener('click', function(e) {
            e.preventDefault();
            targetImage.value = '';
            targetPreview.classList.add('hidden');
            targetPlaceholder.classList.remove('hidden');
        });
        
        // 显示进度条
        function showProgress() {
            progressContainer.classList.remove('hidden');
            resultDiv.classList.add('hidden');
            updateProgress(0, '准备中...');
        }
        
        // 更新进度
        function updateProgress(percent, status) {
            progressFill.style.width = percent + '%';
            progressPercent.textContent = percent + '%';
            statusText.innerHTML = `<i class="fa fa-circle-o-notch fa-spin mr-2"></i>${status}`;
        }
        
        // 隐藏进度条
        function hideProgress() {
            progressContainer.classList.add('hidden');
            if (checkInterval) {
                clearInterval(checkInterval);
                checkInterval = null;
            }
        }
        
        // 检查任务状态
        async function checkTaskStatus(taskId) {
            try {
                // 模拟API调用，实际项目中替换为真实接口
                const response = await fetch(`/api/check_task/${taskId}`);
                const data = await response.json();
                
                if (data.response && data.response.code === 0) {
                    const responseData = data.response.data;
                    
                    // 检查是否有文件URL（任务完成）
                    if (Array.isArray(responseData) && responseData.length > 0) {
                        const fileUrl = responseData[0].fileUrl;
                        if (fileUrl) {
                            hideProgress();
                            resultImage.src = fileUrl;
                            downloadLink.href = fileUrl;
                            downloadLink.download = 'ai-generated-image.jpg';
                            resultDiv.classList.remove('hidden');
                            return true;
                        }
                    }
                    
                    // 检查任务状态
                    if (responseData.taskStatus) {
                        let status = '';
                        let progress = 0;
                        
                        switch(responseData.taskStatus) {
                            case 'QUEUED':
                                status = '任务已排队，等待执行...';
                                progress = 20;
                                break;
                            case 'RUNNING':
                                status = '任务正在执行中...';
                                progress = 50;
                                break;
                            case 'PROCESSING':
                                status = '正在处理图像...';
                                progress = 70;
                                break;
                            case 'COMPLETED':
                                status = '任务已完成，正在获取结果...';
                                progress = 90;
                                break;
                            default:
                                status = `任务状态: ${responseData.taskStatus}`;
                                progress = 30;
                        }
                        
                        updateProgress(progress, status);
                    }
                }
                
                return false; // 任务未完成，继续轮询
            } catch (error) {
                console.error('检查任务状态失败:', error);
                updateProgress(0, `出错了: ${error.message}`);
                return false;
            }
        }
        
        // 表单提交处理
        form.onsubmit = async function(e) {
            e.preventDefault();
            
            // const workflowId = document.getElementById('workflowId').value.trim();
            const originalFile = document.getElementById('originalImage').files[0];
            const targetFile = document.getElementById('targetImage').files[0];
            
            // if (!workflowId || !originalFile || !targetFile) {
            //     alert('请填写Workflow ID并上传两张图片');
            //     return;
            // }
            
            // 显示进度
            showProgress();
            updateProgress(10, '正在上传原图...');
            
            try {
                // 上传原图
                let formData1 = new FormData();
                formData1.append('file', originalFile);
                let resp1 = await fetch('/api/upload', {method: 'POST', body: formData1});
                let data1 = await resp1.json();
                
                if (!data1.fileName) {
                    hideProgress();
                    alert('原图上传失败：' + (data1.error || data1.raw_response || '未知错误'));
                    return;
                }
                
                updateProgress(30, '原图上传成功，正在上传目标图...');
                
                // 上传目标图
                let formData2 = new FormData();
                formData2.append('file', targetFile);
                let resp2 = await fetch('/api/upload', {method: 'POST', body: formData2});
                let data2 = await resp2.json();
                
                if (!data2.fileName) {
                    hideProgress();
                    alert('目标图上传失败：' + (data2.error || data2.raw_response || '未知错误'));
                    return;
                }
                
                updateProgress(50, '图片上传成功，正在创建任务...');
                
                // 创建任务
                let formData3 = new FormData();
                // formData3.append('workflow_id', workflowId);
                formData3.append('original_file_name', data1.fileName);
                formData3.append('target_file_name', data2.fileName);
                let resp3 = await fetch('/api/create_task', {method: 'POST', body: formData3});
                let data3 = await resp3.json();
                
                if (data3.taskId) {
                    updateProgress(60, '任务创建成功，等待处理...');
                    
                    // 开始轮询任务状态
                    checkInterval = setInterval(async () => {
                        const isCompleted = await checkTaskStatus(data3.taskId);
                        if (isCompleted) {
                            // 任务完成，清理定时器
                            if (checkInterval) {
                                clearInterval(checkInterval);
                                checkInterval = null;
                            }
                        }
                    }, 3000); // 每3秒检查一次
                    
                } else {
                    hideProgress();
                    alert('任务创建失败：' + (data3.error || '未知错误'));
                }
                
            } catch (error) {
                hideProgress();
                alert('处理失败：' + error.message);
            }
        }
    </script>
</body>
</html>
