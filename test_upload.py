import requests
import os

def test_upload():
    """
    测试文件上传接口
    """
    url = "http://127.0.0.1:8000/upload"
    
    # 读取本地文件
    test_file_path = "./WechatIMG1066.jpg"
    
    # 检查文件是否存在
    if not os.path.exists(test_file_path):
        print(f"错误：文件 {test_file_path} 不存在")
        return
    
    try:
        files = {
            'file': open(test_file_path, 'rb')
        }
        data = {
            'api_key': '979550fb2b0047509bc9187fbb75d0e2',  # 请替换为实际的API密钥
            'file_type': 'image'
        }
        
        print("正在测试文件上传接口...")
        response = requests.post(url, files=files, data=data)
        
        print(f"状态码: {response.status_code}")
        print(f"图片名: {test_file_path}")
        print(f"响应内容: {response.json()}")
        
    except Exception as e:
        print(f"测试失败: {e}")
    
    finally:
        # 关闭文件
        if 'files' in locals() and 'file' in files:
            files['file'].close()

if __name__ == "__main__":
    test_upload() 